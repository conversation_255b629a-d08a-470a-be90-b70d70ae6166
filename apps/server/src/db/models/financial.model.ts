import mongoose from "mongoose"

const { Schema, model } = mongoose

// Category Schema
const categorySchema = new Schema(
  {
    // Remove the custom _id definition to use MongoDB's default ObjectId
    name: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ["income", "expense"],
    },
    color: { type: String, default: "#3B82F6" }, // Default blue color
    icon: { type: String, default: "DollarSign" }, // Lucide icon name
    userId: { type: String, ref: "User", required: true },
    isDefault: { type: Boolean, default: false }, // System default categories
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
  },
  { collection: "category" }
)

// Transaction Schema
const transactionSchema = new Schema(
  {
    // Remove the custom _id definition to use MongoDB's default ObjectId
    amount: {
      type: Number,
      required: true,
      min: [0.01, "Amount must be greater than 0"],
    },
    type: {
      type: String,
      required: true,
      enum: ["income", "expense"],
    },
    description: { type: String, required: true },
    categoryId: { type: String, ref: "Category", required: true },
    userId: { type: String, ref: "User", required: true },
    date: { type: Date, required: true, default: Date.now },
    tags: [{ type: String }], // Optional tags for better organization
    recurring: {
      enabled: { type: Boolean, default: false },
      frequency: {
        type: String,
        enum: ["daily", "weekly", "monthly", "yearly"],
        default: "monthly",
      },
      nextDate: { type: Date },
    },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
  },
  { collection: "transaction" }
)

// Budget Schema
const budgetSchema = new Schema(
  {
    // Remove the custom _id definition to use MongoDB's default ObjectId
    name: { type: String, required: true },
    amount: {
      type: Number,
      required: true,
      min: [0, "Budget amount cannot be negative"],
    },
    period: {
      type: String,
      required: true,
      enum: ["weekly", "monthly", "quarterly", "yearly"],
      default: "monthly",
    },
    categoryIds: [{ type: String, ref: "Category" }], // Categories included in this budget
    userId: { type: String, ref: "User", required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    alertThreshold: {
      type: Number,
      default: 80,
      min: [0, "Alert threshold cannot be negative"],
      max: [100, "Alert threshold cannot exceed 100%"],
    }, // Percentage at which to alert user
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
  },
  { collection: "budget" }
)

// Add indexes for better query performance
categorySchema.index({ userId: 1, type: 1 })
categorySchema.index({ userId: 1, name: 1 }, { unique: true })

transactionSchema.index({ userId: 1, date: -1 })
transactionSchema.index({ userId: 1, categoryId: 1 })
transactionSchema.index({ userId: 1, type: 1, date: -1 })

budgetSchema.index({ userId: 1, isActive: 1 })
budgetSchema.index({ userId: 1, startDate: 1, endDate: 1 })

// Pre-save middleware to update the updatedAt field
categorySchema.pre("save", function (next) {
  this.updatedAt = new Date()
  next()
})

transactionSchema.pre("save", function (next) {
  this.updatedAt = new Date()
  next()
})

budgetSchema.pre("save", function (next) {
  this.updatedAt = new Date()
  next()
})

// Create models
const Category = model("Category", categorySchema)
const Transaction = model("Transaction", transactionSchema)
const Budget = model("Budget", budgetSchema)

export { Category, Transaction, Budget }

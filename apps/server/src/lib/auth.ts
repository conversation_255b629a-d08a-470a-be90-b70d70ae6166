import { betterAuth } from "better-auth"
import { mongodbAdapter } from "better-auth/adapters/mongodb"

import { client } from "../db"
import { createUserCategories } from "./seed-categories"

export const auth = betterAuth({
  database: mongodbAdapter(client),
  trustedOrigins: [process.env.CORS_ORIGIN || ""],
  emailAndPassword: {
    enabled: true,
  },
  databaseHooks: {
    user: {
      create: {
        after: async (user) => {
          // Create default categories for the new user
          await createUserCategories(user.id)
        },
      },
    },
  },
})

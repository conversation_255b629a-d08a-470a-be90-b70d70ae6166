import { z } from "zod"
import { TRPCError } from "@trpc/server"

import { Transaction } from "@/db/models/financial.model"
import { getAnalyticsSchema } from "@/lib/schemas"
import { protectedProcedure, router } from "@/lib/trpc"

export const analyticsRouter = router({
  // Get spending trends over time
  getSpendingTrends: protectedProcedure.input(getAnalyticsSchema).query(async ({ ctx, input }) => {
    try {
      const { startDate, endDate, groupBy } = input

      // Define grouping format based on groupBy parameter
      const groupFormat = {
        day: { $dateToString: { format: "%Y-%m-%d", date: "$date" } },
        week: { $dateToString: { format: "%Y-W%U", date: "$date" } },
        month: { $dateToString: { format: "%Y-%m", date: "$date" } },
        year: { $dateToString: { format: "%Y", date: "$date" } },
      }

      const trends = await Transaction.aggregate([
        {
          $match: {
            userId: ctx.session.user.id,
            date: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: {
              period: groupFormat[groupBy],
              type: "$type",
            },
            total: { $sum: "$amount" },
            count: { $sum: 1 },
          },
        },
        {
          $group: {
            _id: "$_id.period",
            income: {
              $sum: {
                $cond: [{ $eq: ["$_id.type", "income"] }, "$total", 0],
              },
            },
            expenses: {
              $sum: {
                $cond: [{ $eq: ["$_id.type", "expense"] }, "$total", 0],
              },
            },
            incomeCount: {
              $sum: {
                $cond: [{ $eq: ["$_id.type", "income"] }, "$count", 0],
              },
            },
            expenseCount: {
              $sum: {
                $cond: [{ $eq: ["$_id.type", "expense"] }, "$count", 0],
              },
            },
          },
        },
        {
          $addFields: {
            balance: { $subtract: ["$income", "$expenses"] },
            totalTransactions: { $add: ["$incomeCount", "$expenseCount"] },
          },
        },
        { $sort: { _id: 1 } },
      ])

      return trends.map((trend) => ({
        period: trend._id,
        income: trend.income,
        expenses: trend.expenses,
        balance: trend.balance,
        incomeCount: trend.incomeCount,
        expenseCount: trend.expenseCount,
        totalTransactions: trend.totalTransactions,
      }))
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch spending trends",
      })
    }
  }),

  // Get category breakdown
  getCategoryBreakdown: protectedProcedure
    .input(getAnalyticsSchema.pick({ startDate: true, endDate: true }))
    .query(async ({ ctx, input }) => {
      try {
        const { startDate, endDate } = input

        const breakdown = await Transaction.aggregate([
          {
            $match: {
              userId: ctx.session.user.id,
              date: { $gte: startDate, $lte: endDate },
            },
          },
          {
            $lookup: {
              from: "category",
              localField: "categoryId",
              foreignField: "_id",
              as: "category",
            },
          },
          { $unwind: "$category" },
          {
            $group: {
              _id: {
                categoryId: "$categoryId",
                type: "$type",
              },
              categoryName: { $first: "$category.name" },
              categoryColor: { $first: "$category.color" },
              categoryIcon: { $first: "$category.icon" },
              total: { $sum: "$amount" },
              count: { $sum: 1 },
              avgAmount: { $avg: "$amount" },
            },
          },
          {
            $group: {
              _id: "$_id.type",
              categories: {
                $push: {
                  categoryId: "$_id.categoryId",
                  name: "$categoryName",
                  color: "$categoryColor",
                  icon: "$categoryIcon",
                  total: "$total",
                  count: "$count",
                  avgAmount: "$avgAmount",
                },
              },
              totalAmount: { $sum: "$total" },
            },
          },
        ])

        const result = {
          income: { categories: [], totalAmount: 0 },
          expenses: { categories: [], totalAmount: 0 },
        }

        breakdown.forEach((item) => {
          if (item._id === "income") {
            result.income = {
              categories: item.categories.sort((a: any, b: any) => b.total - a.total),
              totalAmount: item.totalAmount,
            }
          } else if (item._id === "expense") {
            result.expenses = {
              categories: item.categories.sort((a: any, b: any) => b.total - a.total),
              totalAmount: item.totalAmount,
            }
          }
        })

        return result
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch category breakdown",
        })
      }
    }),

  // Get monthly comparison
  getMonthlyComparison: protectedProcedure
    .input(getAnalyticsSchema.pick({ startDate: true, endDate: true }))
    .query(async ({ ctx, input }) => {
      try {
        const { startDate, endDate } = input

        const comparison = await Transaction.aggregate([
          {
            $match: {
              userId: ctx.session.user.id,
              date: { $gte: startDate, $lte: endDate },
            },
          },
          {
            $group: {
              _id: {
                month: { $dateToString: { format: "%Y-%m", date: "$date" } },
                type: "$type",
              },
              total: { $sum: "$amount" },
              count: { $sum: 1 },
            },
          },
          {
            $group: {
              _id: "$_id.month",
              income: {
                $sum: {
                  $cond: [{ $eq: ["$_id.type", "income"] }, "$total", 0],
                },
              },
              expenses: {
                $sum: {
                  $cond: [{ $eq: ["$_id.type", "expense"] }, "$total", 0],
                },
              },
            },
          },
          {
            $addFields: {
              balance: { $subtract: ["$income", "$expenses"] },
              savingsRate: {
                $cond: [
                  { $gt: ["$income", 0] },
                  {
                    $multiply: [
                      {
                        $divide: [{ $subtract: ["$income", "$expenses"] }, "$income"],
                      },
                      100,
                    ],
                  },
                  0,
                ],
              },
            },
          },
          { $sort: { _id: 1 } },
        ])

        return comparison.map((month) => ({
          month: month._id,
          income: month.income,
          expenses: month.expenses,
          balance: month.balance,
          savingsRate: Math.round(month.savingsRate * 100) / 100,
        }))
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch monthly comparison",
        })
      }
    }),

  // Get top spending categories
  getTopSpendingCategories: protectedProcedure
    .input(
      getAnalyticsSchema.pick({ startDate: true, endDate: true }).extend({
        limit: z.number().min(1).max(50).optional().default(5),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { startDate, endDate, limit } = input

        const topCategories = await Transaction.aggregate([
          {
            $match: {
              userId: ctx.session.user.id,
              type: "expense",
              date: { $gte: startDate, $lte: endDate },
            },
          },
          {
            $lookup: {
              from: "category",
              localField: "categoryId",
              foreignField: "_id",
              as: "category",
            },
          },
          { $unwind: "$category" },
          {
            $group: {
              _id: "$categoryId",
              categoryName: { $first: "$category.name" },
              categoryColor: { $first: "$category.color" },
              categoryIcon: { $first: "$category.icon" },
              total: { $sum: "$amount" },
              count: { $sum: 1 },
              avgAmount: { $avg: "$amount" },
            },
          },
          { $sort: { total: -1 } },
          { $limit: limit },
        ])

        return topCategories.map((category) => ({
          categoryId: category._id,
          name: category.categoryName,
          color: category.categoryColor,
          icon: category.categoryIcon,
          total: category.total,
          count: category.count,
          avgAmount: Math.round(category.avgAmount * 100) / 100,
        }))
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch top spending categories",
        })
      }
    }),

  // Get financial summary
  getFinancialSummary: protectedProcedure
    .input(getAnalyticsSchema.pick({ startDate: true, endDate: true }))
    .query(async ({ ctx, input }) => {
      try {
        const { startDate, endDate } = input

        const summary = await Transaction.aggregate([
          {
            $match: {
              userId: ctx.session.user.id,
              date: { $gte: startDate, $lte: endDate },
            },
          },
          {
            $group: {
              _id: "$type",
              total: { $sum: "$amount" },
              count: { $sum: 1 },
              avgAmount: { $avg: "$amount" },
              maxAmount: { $max: "$amount" },
              minAmount: { $min: "$amount" },
            },
          },
        ])

        const income = summary.find((s) => s._id === "income") || {
          total: 0,
          count: 0,
          avgAmount: 0,
          maxAmount: 0,
          minAmount: 0,
        }
        const expenses = summary.find((s) => s._id === "expense") || {
          total: 0,
          count: 0,
          avgAmount: 0,
          maxAmount: 0,
          minAmount: 0,
        }

        const balance = income.total - expenses.total
        const savingsRate = income.total > 0 ? (balance / income.total) * 100 : 0

        return {
          income: {
            total: income.total,
            count: income.count,
            avgAmount: Math.round(income.avgAmount * 100) / 100,
            maxAmount: income.maxAmount,
            minAmount: income.minAmount,
          },
          expenses: {
            total: expenses.total,
            count: expenses.count,
            avgAmount: Math.round(expenses.avgAmount * 100) / 100,
            maxAmount: expenses.maxAmount,
            minAmount: expenses.minAmount,
          },
          balance,
          savingsRate: Math.round(savingsRate * 100) / 100,
          totalTransactions: income.count + expenses.count,
        }
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch financial summary",
        })
      }
    }),
})

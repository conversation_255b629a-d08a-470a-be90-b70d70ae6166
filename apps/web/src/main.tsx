import { QueryClientProvider } from "@tanstack/react-query"
import { RouterProvider, createRouter } from "@tanstack/react-router"
import ReactD<PERSON> from "react-dom/client"

import { routeTree } from "./routeTree.gen"
import { queryClient, trpc } from "./utils/trpc"
import { authClient } from "./lib/auth-client"

const router = createRouter({
  routeTree,
  defaultPreload: "intent",
  context: {
    trpc,
    queryClient,
    authClient: undefined!,
  },
  Wrap: function WrapComponent({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  },
})

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById("app")

if (!rootElement) {
  throw new Error("Root element not found")
}

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)

  const session = authClient.useSession()
  // Inject the returned value from the hook into the router context
  root.render(<RouterProvider router={router} context={{ authClient: session }} />)
}

import { QueryClientProvider } from "@tanstack/react-query"
import { RouterProvider, createRouter } from "@tanstack/react-router"
import ReactDOM from "react-dom/client"

import { routeTree } from "./routeTree.gen"
import { queryClient, trpc } from "./utils/trpc"
import { authClient } from "./lib/auth-client"

const router = createRouter({
  routeTree,
  defaultPreload: "intent",
  context: {
    trpc,
    queryClient,
    authClient: undefined!,
  },
  Wrap: function WrapComponent({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  },
})

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router
  }
}

// App component that properly calls the useSession hook inside a React component
function App() {
  const session = authClient.useSession()

  return (
    <RouterProvider
      router={router}
      context={{
        trpc,
        queryClient,
        authClient: session,
      }}
    />
  )
}

const rootElement = document.getElementById("app")

if (!rootElement) {
  throw new Error("Root element not found")
}

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(<App />)
}

import { cn } from "@/lib/utils"
import { useMutation, useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import {
  AlertTriangle,
  Calendar,
  DollarSign,
  Edit,
  Eye,
  Filter,
  Plus,
  Search,
  Target,
  Trash2,
  TrendingUp,
} from "lucide-react"
import { useState } from "react"

import { BudgetDetailsDialog } from "@/components/budgets/BudgetDetailsDialog"
import { CreateBudgetDialog } from "@/components/budgets/CreateBudgetDialog"
import { EditBudgetDialog } from "@/components/budgets/EditBudgetDialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { trpc } from "@/utils/trpc"

export const Route = createFileRoute("/_sidebar/budgets/")({
  component: BudgetsPage,
})

function BudgetsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [periodFilter, setPeriodFilter] = useState<string>("all")
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [selectedBudget, setSelectedBudget] = useState<any>(null)

  // Fetch budgets data
  const { data: budgets = [], isLoading, refetch } = useQuery(trpc.budgets.getAll.queryOptions())

  const { data: budgetOverview, isLoading: overviewLoading } = useQuery(trpc.budgets.getOverview.queryOptions())

  // Delete budget mutation
  const deleteBudgetMutation = useMutation(
    trpc.budgets.delete.mutationOptions({
      onSuccess: () => {
        refetch()
      },
    })
  )

  // Filter budgets based on search and filters
  const filteredBudgets = budgets.filter((budget) => {
    const matchesSearch = budget.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" && budget.isActive) ||
      (statusFilter === "inactive" && !budget.isActive)

    const matchesPeriod = periodFilter === "all" || budget.period === periodFilter

    return matchesSearch && matchesStatus && matchesPeriod
  })

  const handleEditBudget = (budget: any) => {
    setSelectedBudget(budget)
    setEditDialogOpen(true)
  }

  const handleViewDetails = (budget: any) => {
    setSelectedBudget(budget)
    setDetailsDialogOpen(true)
  }

  const handleDeleteBudget = async (budgetId: string) => {
    if (confirm("Are you sure you want to delete this budget?")) {
      await deleteBudgetMutation.mutateAsync({ id: budgetId })
    }
  }

  const getBudgetStatus = (budget: any) => {
    if (!budget.isActive) return "inactive"

    const now = new Date()
    const startDate = new Date(budget.startDate)
    const endDate = new Date(budget.endDate)

    if (now < startDate) return "upcoming"
    if (now > endDate) return "expired"
    return "active"
  }

  const getBudgetStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "upcoming":
        return "bg-blue-100 text-blue-800"
      case "expired":
        return "bg-gray-100 text-gray-800"
      case "inactive":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Budget Management</h1>
          <p className="text-muted-foreground">
            Create and manage your budgets to stay on track with your financial goals
          </p>
        </div>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Budget
        </Button>
      </div>

      {/* Budget Overview Cards */}
      {!overviewLoading && budgetOverview && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Budgets</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{budgetOverview.summary.activeBudgetCount}</div>
              <p className="text-xs text-muted-foreground">
                {budgetOverview.summary.overBudgetCount > 0 && (
                  <span className="text-red-600">{budgetOverview.summary.overBudgetCount} over budget</span>
                )}
                {budgetOverview.summary.overBudgetCount === 0 && "All on track"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Budgeted</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${budgetOverview.summary.totalBudgeted.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${budgetOverview.summary.totalSpent.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                {budgetOverview.summary.totalBudgeted > 0 && (
                  <span>
                    {((budgetOverview.summary.totalSpent / budgetOverview.summary.totalBudgeted) * 100).toFixed(1)}% of
                    budget
                  </span>
                )}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Remaining</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${budgetOverview.summary.totalRemaining}</div>
              <p className="text-xs text-muted-foreground">Available to spend</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search budgets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select value={periodFilter} onValueChange={setPeriodFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Periods</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Budget List */}
      <div className="space-y-4">
        {filteredBudgets.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {budgets.length === 0 ? "No budgets yet" : "No budgets match your filters"}
                </h3>
                <p className="text-gray-500 mb-4">
                  {budgets.length === 0
                    ? "Create your first budget to start tracking your spending goals"
                    : "Try adjusting your search or filter criteria"}
                </p>
                {budgets.length === 0 && (
                  <Button onClick={() => setCreateDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Budget
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredBudgets.map((budget) => {
            const status = getBudgetStatus(budget)
            const statusColor = getBudgetStatusColor(status)

            return (
              <BudgetCard
                key={budget._id.toString()}
                budget={budget}
                status={status}
                statusColor={statusColor}
                onEdit={() => handleEditBudget(budget)}
                onDelete={() => handleDeleteBudget(budget._id.toString())}
                onViewDetails={() => handleViewDetails(budget)}
              />
            )
          })
        )}
      </div>

      {/* Dialogs */}
      <CreateBudgetDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSuccess={() => {
          refetch()
          setCreateDialogOpen(false)
        }}
      />

      {selectedBudget && (
        <>
          <EditBudgetDialog
            open={editDialogOpen}
            onOpenChange={setEditDialogOpen}
            budget={selectedBudget}
            onSuccess={() => {
              refetch()
              setEditDialogOpen(false)
              setSelectedBudget(null)
            }}
          />

          <BudgetDetailsDialog
            open={detailsDialogOpen}
            onOpenChange={setDetailsDialogOpen}
            budget={selectedBudget}
            onClose={() => {
              setDetailsDialogOpen(false)
              setSelectedBudget(null)
            }}
          />
        </>
      )}
    </div>
  )
}

// Budget Card Component
function BudgetCard({
  budget,
  status,
  statusColor,
  onEdit,
  onDelete,
  onViewDetails,
}: {
  budget: any
  status: string
  statusColor: string
  onEdit: () => void
  onDelete: () => void
  onViewDetails: () => void
}) {
  // Calculate progress (this would come from the backend in a real implementation)
  const progress = 65 // Placeholder
  const isOverBudget = progress > 100

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="pt-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-lg font-semibold">{budget.name}</h3>
              <Badge className={statusColor}>{status}</Badge>
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span className="flex items-center gap-1">
                <DollarSign className="h-3 w-3" />${budget.amount.toLocaleString()}
              </span>
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {budget.period}
              </span>
              <span>{budget.categoryIds?.length || 0} categories</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={onViewDetails}>
              <Eye className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onEdit}>
              <Edit className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onDelete}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span className={cn(isOverBudget ? "text-red-600" : "text-muted-foreground")}>{progress}%</span>
          </div>
          <Progress value={Math.min(progress, 100)} className={cn(isOverBudget ? "bg-red-100" : "")} />
          {isOverBudget && (
            <Alert className="mt-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>This budget is over the limit. Consider reviewing your spending.</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Date Range */}
        <div className="mt-4 text-xs text-muted-foreground">
          {new Date(budget.startDate).toLocaleDateString()} - {new Date(budget.endDate).toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  )
}

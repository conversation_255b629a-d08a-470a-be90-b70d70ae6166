import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from "@/components/ui/chart"
import { Separator } from "@/components/ui/separator"
import { trpc } from "@/utils/trpc"
import { useQuery } from "@tanstack/react-query"
import { createFileRoute, redirect } from "@tanstack/react-router"
import {
  ArrowDownRight,
  ArrowUpRight,
  BarChart3,
  PlusCircle,
  Receipt,
  Target,
  TrendingDown,
  TrendingUp,
  Wallet,
} from "lucide-react"
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Cell, Pie, PieChart, XAxis, YAxis } from "recharts"

export const Route = createFileRoute("/_sidebar/dashboard/")({
  loader: ({ context }) => {
    const { data } = context.authClient

    if (!data) {
      throw redirect({
        to: "/login",
        search: {
          // Use the current location to power a redirect after login
          // (Do not use `router.state.resolvedLocation` as it can
          // potentially lag behind the actual current location)
          redirect: location.href,
        },
      })
    }

    return context.authClient
  },
  component: RouteComponent,
})

function RouteComponent() {
  const { data: session } = Route.useLoaderData()

  // Get financial summary for current month
  const currentMonth = new Date()
  const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
  const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0)

  const transactionSummary = useQuery(
    trpc.transactions.getSummary.queryOptions({
      startDate: startOfMonth,
      endDate: endOfMonth,
    })
  )

  const recentTransactions = useQuery(
    trpc.transactions.getAll.queryOptions({
      page: 1,
      limit: 5,
    })
  )

  const budgetOverview = useQuery(trpc.budgets.getOverview.queryOptions())

  // Get analytics data for charts
  const spendingTrends = useQuery(
    trpc.analytics.getSpendingTrends.queryOptions({
      startDate: new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 5, 1),
      endDate: endOfMonth,
      groupBy: "month",
    })
  )

  const topSpendingCategories = useQuery(
    trpc.analytics.getTopSpendingCategories.queryOptions({
      startDate: startOfMonth,
      endDate: endOfMonth,
      limit: 5,
    })
  )

  // Chart configurations
  const monthlyTrendsConfig = {
    income: {
      label: "Income",
      color: "hsl(142, 76%, 36%)", // Green for income
    },
    expenses: {
      label: "Expenses",
      color: "hsl(0, 84%, 60%)", // Red for expenses
    },
    netSavings: {
      label: "Net Savings",
      color: "hsl(217, 91%, 60%)", // Blue for net savings
    },
  } satisfies ChartConfig

  const categoryConfig = {
    amount: {
      label: "Amount",
    },
  } satisfies ChartConfig

  // Enhanced chart configurations for new charts
  const savingsConfig = {
    savings: {
      label: "Cumulative Savings",
      color: "hsl(217, 91%, 60%)",
    },
  } satisfies ChartConfig

  const summary = transactionSummary.data || {
    income: 0,
    expenses: 0,
    balance: 0,
    totalTransactions: 0,
  }
  const transactions = recentTransactions.data?.transactions || []
  const budgets = budgetOverview.data?.summary || {
    totalBudgeted: 0,
    totalSpent: 0,
    activeBudgetCount: 0,
    overBudgetCount: 0,
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* Header */}
      <header className="bg-background">
        <div className="flex items-center justify-between h-16">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Welcome back, {session?.user.name}!</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Here's your financial overview for{" "}
              {currentMonth.toLocaleDateString("en-US", {
                month: "long",
                year: "numeric",
              })}
            </p>
          </div>

          <Button className="flex items-center gap-2">
            <PlusCircle className="h-4 w-4" />
            Add Transaction
          </Button>
        </div>
      </header>

      {/* Main content area */}
      <main className="flex-1 overflow-y-auto">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Balance */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                $
                {summary.balance.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                })}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.balance >= 0 ? "+" : ""}$
                {summary.balance.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                })}{" "}
                from last month
              </p>
            </CardContent>
          </Card>

          {/* Total Income */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Income</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                +$
                {summary.income.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                })}
              </div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          {/* Total Expenses */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expenses</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                -$
                {summary.expenses.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                })}
              </div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>

          {/* Active Budgets */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Budgets</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{budgets.activeBudgetCount}</div>
              <p className="text-xs text-muted-foreground">
                {budgets.overBudgetCount > 0 && (
                  <span className="text-red-600">{budgets.overBudgetCount} over budget</span>
                )}
                {budgets.overBudgetCount === 0 && "All on track"}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Enhanced Spending Trends Chart */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Spending Trends
                </CardTitle>
                <CardDescription>Monthly income vs expenses over the last 6 months</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  6 Months
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <ChartContainer config={monthlyTrendsConfig} className="h-[350px] w-full">
                <BarChart
                  accessibilityLayer
                  data={spendingTrends.data || []}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="incomeGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="hsl(142, 76%, 36%)" stopOpacity={0.8} />
                      <stop offset="100%" stopColor="hsl(142, 76%, 36%)" stopOpacity={0.3} />
                    </linearGradient>
                    <linearGradient id="expensesGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="hsl(0, 84%, 60%)" stopOpacity={0.8} />
                      <stop offset="100%" stopColor="hsl(0, 84%, 60%)" stopOpacity={0.3} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
                  <XAxis
                    dataKey="period"
                    tickLine={false}
                    tickMargin={10}
                    axisLine={false}
                    fontSize={12}
                    tickFormatter={(value) => {
                      // Format month names to show first 3 characters
                      return value.slice(0, 3)
                    }}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    fontSize={12}
                    tickFormatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <ChartTooltip
                    content={
                      <ChartTooltipContent formatter={(value, name) => [`$${Number(value).toLocaleString()}`, name]} />
                    }
                  />
                  <ChartLegend content={<ChartLegendContent />} />
                  <Bar dataKey="income" fill="url(#incomeGradient)" radius={[4, 4, 0, 0]} name="Income" />
                  <Bar dataKey="expenses" fill="url(#expensesGradient)" radius={[4, 4, 0, 0]} name="Expenses" />
                </BarChart>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Enhanced Top Spending Categories */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Top Spending Categories
                </CardTitle>
                <CardDescription>Your highest expense categories this month</CardDescription>
              </div>
              <Badge variant="outline" className="text-xs">
                Top 5
              </Badge>
            </CardHeader>
            <CardContent>
              {topSpendingCategories.data && topSpendingCategories.data.length > 0 ? (
                <ChartContainer config={categoryConfig} className="h-[350px] w-full">
                  <PieChart>
                    <Pie
                      data={topSpendingCategories.data}
                      dataKey="total"
                      nameKey="categoryName"
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      innerRadius={40}
                      paddingAngle={2}
                      animationBegin={0}
                      animationDuration={800}
                    >
                      {topSpendingCategories.data.map((_: any, index: number) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={`hsl(${(index * 137.5) % 360}, 70%, 50%)`}
                          stroke="white"
                          strokeWidth={2}
                        />
                      ))}
                    </Pie>
                    <ChartTooltip
                      content={
                        <ChartTooltipContent
                          formatter={(value, name) => [`$${Number(value).toLocaleString()}`, name]}
                          labelFormatter={(label) => `Category: ${label}`}
                        />
                      }
                    />
                    <ChartLegend content={<ChartLegendContent />} wrapperStyle={{ paddingTop: "20px" }} />
                  </PieChart>
                </ChartContainer>
              ) : (
                <div className="flex flex-col items-center justify-center h-[350px] text-center">
                  <div className="rounded-full bg-muted p-4 mb-4">
                    <Target className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">No spending data</h3>
                  <p className="text-muted-foreground text-sm max-w-sm">
                    Add some expense transactions to see your spending breakdown by category.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* New Savings Trend Chart */}
        <div className="mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Savings Trend
                </CardTitle>
                <CardDescription>Your net savings (income - expenses) over time</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  6 Months
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <ChartContainer config={savingsConfig} className="h-[300px] w-full">
                <AreaChart
                  data={
                    spendingTrends.data?.map((item: any) => ({
                      ...item,
                      savings: (item.income || 0) - (item.expenses || 0),
                    })) || []
                  }
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="savingsGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="hsl(217, 91%, 60%)" stopOpacity={0.3} />
                      <stop offset="100%" stopColor="hsl(217, 91%, 60%)" stopOpacity={0.05} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis
                    dataKey="period"
                    tickLine={false}
                    axisLine={false}
                    fontSize={12}
                    tickFormatter={(value) => value.slice(0, 3)}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    fontSize={12}
                    tickFormatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <ChartTooltip
                    content={
                      <ChartTooltipContent
                        formatter={(value) => [`$${Number(value).toLocaleString()}`, "Net Savings"]}
                      />
                    }
                  />
                  <Area
                    type="monotone"
                    dataKey="savings"
                    stroke="hsl(217, 91%, 60%)"
                    strokeWidth={2}
                    fill="url(#savingsGradient)"
                    dot={{ fill: "hsl(217, 91%, 60%)", strokeWidth: 2, r: 4 }}
                    activeDot={{
                      r: 6,
                      stroke: "hsl(217, 91%, 60%)",
                      strokeWidth: 2,
                    }}
                  />
                </AreaChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Financial Summary */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Savings Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {summary.income > 0
                  ? `${(((summary.income - summary.expenses) / summary.income) * 100).toFixed(1)}%`
                  : "0%"}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.income - summary.expenses >= 0 ? "Positive savings" : "Overspending"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Daily Spending</CardTitle>
              <Receipt className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                $
                {(
                  summary.expenses / new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).getDate()
                ).toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                })}
              </div>
              <p className="text-xs text-muted-foreground">Per day this month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transaction Count</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalTransactions}</div>
              <p className="text-xs text-muted-foreground">Total transactions</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {budgets.totalBudgeted > 0
                  ? `${((budgets.totalSpent / budgets.totalBudgeted) * 100).toFixed(1)}%`
                  : "0%"}
              </div>
              <p className="text-xs text-muted-foreground">Of total budget used</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Transactions */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Transactions</CardTitle>
                  <CardDescription>Your latest financial activity</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {transactions.length === 0 ? (
                <div className="text-center py-8">
                  <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No transactions yet</p>
                  <p className="text-sm text-gray-400">Add your first transaction to get started</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {transactions.map((transaction: any) => (
                    <div key={transaction._id} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`p-2 rounded-full ${
                            transaction.type === "income" ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"
                          }`}
                        >
                          {transaction.type === "income" ? (
                            <ArrowUpRight className="h-4 w-4" />
                          ) : (
                            <ArrowDownRight className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-gray-500">{new Date(transaction.date).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p
                          className={`font-semibold ${
                            transaction.type === "income" ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {transaction.type === "income" ? "+" : "-"}$
                          {transaction.amount.toLocaleString("en-US", {
                            minimumFractionDigits: 2,
                          })}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions & Budget Status */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Income
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Expense
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Target className="mr-2 h-4 w-4" />
                  Create Budget
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Analytics
                </Button>
              </CardContent>
            </Card>

            {/* Budget Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Budget Overview</CardTitle>
                <CardDescription>This month's budget status</CardDescription>
              </CardHeader>
              <CardContent>
                {budgets.activeBudgetCount === 0 ? (
                  <div className="text-center py-4">
                    <Target className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">No active budgets</p>
                    <Button variant="outline" size="sm" className="mt-2">
                      Create Budget
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Total Budgeted</span>
                      <span className="font-semibold">
                        $
                        {budgets.totalBudgeted.toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Total Spent</span>
                      <span className="font-semibold">
                        $
                        {budgets.totalSpent.toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Remaining</span>
                      <span
                        className={`font-semibold ${
                          budgets.totalBudgeted - budgets.totalSpent >= 0 ? "text-green-600" : "text-red-600"
                        }`}
                      >
                        $
                        {(budgets.totalBudgeted - budgets.totalSpent).toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                        })}
                      </span>
                    </div>
                    {budgets.overBudgetCount > 0 && (
                      <Badge variant="destructive" className="w-full justify-center">
                        {budgets.overBudgetCount} budget
                        {budgets.overBudgetCount > 1 ? "s" : ""} over limit
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
